import os
from flask import Flask
from flask_cors import CORS
from database import db
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

def create_app():
    app = Flask(__name__, static_folder='static', static_url_path='')
    
    # Enable CORS for all routes
    CORS(app)
    
    # Setup secret key
    app.secret_key = os.environ.get("FLASK_SECRET_KEY") or "job-tracker-secret-key"
    
    # Configure database
    app.config["SQLALCHEMY_DATABASE_URI"] = os.environ.get("DATABASE_URL") or "sqlite:///jobtracker.db"
    app.config["SQLALCHEMY_ENGINE_OPTIONS"] = {
        "pool_recycle": 300,
        "pool_pre_ping": True,
    }
    app.config["SQLALCHEMY_TRACK_MODIFICATIONS"] = False
    
    # Initialize extensions
    db.init_app(app)
    
    with app.app_context():
        # Import models to ensure tables are created
        import models
        db.create_all()
    
    # Register blueprints
    from app import api_bp
    app.register_blueprint(api_bp, url_prefix='/api')
    
    # Serve React app
    @app.route('/')
    def index():
        return app.send_static_file('index.html')
    
    return app

if __name__ == '__main__':
    app = create_app()
    app.run(host='0.0.0.0', port=5000, debug=True)

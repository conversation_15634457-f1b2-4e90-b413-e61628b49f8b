import os
import sys
from flask import Flask
from flask_cors import CORS

# Add the current directory to the Python path so we can import local modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import db

def create_app():
    app = Flask(__name__)
    
    # Enable CORS for all routes
    CORS(app)
    
    # Setup secret key from environment
    app.secret_key = os.environ.get("FLASK_SECRET_KEY") or "job-tracker-secret-key"
    
    # Configure database - Vercel will provide DATABASE_URL environment variable
    app.config["SQLALCHEMY_DATABASE_URI"] = os.environ.get("DATABASE_URL") or "sqlite:///jobtracker.db"
    app.config["SQLALCHEMY_ENGINE_OPTIONS"] = {
        "pool_recycle": 300,
        "pool_pre_ping": True,
    }
    app.config["SQLALCHEMY_TRACK_MODIFICATIONS"] = False
    
    # Initialize extensions
    db.init_app(app)
    
    with app.app_context():
        try:
            # Import models to ensure tables are created
            import models
            db.create_all()
        except Exception as e:
            print(f"Database initialization error: {e}")
    
    # Register blueprints - NO url_prefix since Vercel routing handles /api
    try:
        from app import api_bp
        app.register_blueprint(api_bp)
    except Exception as e:
        print(f"Blueprint registration error: {e}")
        # Create a simple health check endpoint as fallback
        @app.route('/health')
        def health():
            return {'status': 'error', 'message': str(e)}
    
    return app

# Create the app instance
app = create_app()

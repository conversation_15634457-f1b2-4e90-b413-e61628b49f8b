import os
import sys
from flask import Flask, jsonify
from flask_cors import CORS

# Add the current directory to the Python path so we can import local modules
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Import local modules
from database import db

def create_app():
    app = Flask(__name__)

    # Enable CORS for all routes
    CORS(app)

    # Setup secret key from environment
    app.secret_key = os.environ.get("FLASK_SECRET_KEY") or "job-tracker-secret-key"

    # Configure database - Vercel will provide DATABASE_URL environment variable
    database_url = os.environ.get("DATABASE_URL")
    if database_url and database_url.startswith("postgres://"):
        # Fix for Heroku/Vercel postgres URL format
        database_url = database_url.replace("postgres://", "postgresql://", 1)

    # For Vercel deployment, use in-memory SQLite if no DATABASE_URL is provided
    if not database_url:
        # Use in-memory SQLite for serverless environments
        database_url = "sqlite:///:memory:"
        print("⚠️  Using in-memory SQLite database (data will not persist)")

    app.config["SQLALCHEMY_DATABASE_URI"] = database_url
    app.config["SQLALCHEMY_ENGINE_OPTIONS"] = {
        "pool_recycle": 300,
        "pool_pre_ping": True,
    }
    app.config["SQLALCHEMY_TRACK_MODIFICATIONS"] = False

    # Initialize extensions
    db.init_app(app)

    # Add a health check endpoint first
    @app.route('/health')
    def health():
        try:
            # Test database connection
            db.session.execute(db.text('SELECT 1'))
            db_status = 'connected'
        except Exception as e:
            db_status = f'disconnected: {str(e)}'

        return jsonify({
            'status': 'ok',
            'message': 'API is running',
            'database': db_status,
            'database_url': app.config.get('SQLALCHEMY_DATABASE_URI', 'not set')[:50] + '...'
        })

    # Add a debug endpoint to check imports
    @app.route('/debug')
    def debug():
        try:
            import models
            return jsonify({
                'status': 'ok',
                'message': 'Models imported successfully',
                'python_path': sys.path[:3]
            })
        except Exception as e:
            return jsonify({
                'status': 'error',
                'message': f'Models import failed: {str(e)}',
                'python_path': sys.path[:3]
            })

    with app.app_context():
        try:
            # Import models to ensure tables are created
            import models
            db.create_all()
            print("✅ Database tables created successfully")
        except Exception as e:
            print(f"❌ Database initialization error: {e}")

    # Register blueprints - NO url_prefix since Vercel routing handles /api
    try:
        # Import the blueprint from the local app.py file
        from app import api_bp
        app.register_blueprint(api_bp)
        print("✅ API blueprint registered successfully")

        # Add a test endpoint to verify blueprint is working
        @app.route('/test')
        def test_endpoint():
            return jsonify({
                'status': 'success',
                'message': 'API is working correctly',
                'endpoints': [str(rule) for rule in app.url_map.iter_rules() if not rule.rule.startswith('/static')]
            })

    except Exception as e:
        print(f"❌ Blueprint registration error: {e}")

        # Create fallback endpoints for debugging
        @app.route('/error')
        def error_info():
            return jsonify({
                'status': 'error',
                'message': f'Blueprint registration failed: {str(e)}',
                'current_dir': current_dir,
                'python_path': sys.path[:5]
            })

    return app

# Create the app instance for Vercel
app = create_app()

# Export the app for Vercel
def handler(request):
    return app(request.environ, lambda status, headers: None)

import csv
import io
from datetime import datetime
from flask import Blueprint, request, jsonify, make_response
from sqlalchemy import or_, and_
from database import db
from models import JobApplication, Tag, JobHistory

api_bp = Blueprint('api', __name__)

# Job Application endpoints
@api_bp.route('/jobs', methods=['GET'])
def get_jobs():
    """Get all job applications with optional filtering"""
    try:
        query = JobApplication.query
        
        # Apply filters
        status = request.args.get('status')
        company = request.args.get('company')
        tag_ids = request.args.getlist('tags')
        search = request.args.get('search')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        
        if status:
            query = query.filter(JobApplication.status == status)
        
        if company:
            query = query.filter(JobApplication.company.ilike(f'%{company}%'))
        
        if tag_ids:
            query = query.join(JobApplication.tags).filter(Tag.id.in_(tag_ids))
        
        if search:
            search_term = f'%{search}%'
            query = query.filter(or_(
                JobApplication.company.ilike(search_term),
                JobApplication.position.ilike(search_term),
                JobApplication.location.ilike(search_term),
                JobApplication.notes.ilike(search_term)
            ))
        
        if date_from:
            query = query.filter(JobApplication.application_date >= datetime.fromisoformat(date_from))
        
        if date_to:
            query = query.filter(JobApplication.application_date <= datetime.fromisoformat(date_to))
        
        # Order by last updated
        jobs = query.order_by(JobApplication.last_updated.desc()).all()
        
        return jsonify([job.to_dict() for job in jobs])
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api_bp.route('/jobs', methods=['POST'])
def create_job():
    """Create a new job application"""
    try:
        data = request.get_json()
        
        if not data.get('company') or not data.get('position'):
            return jsonify({'error': 'Company and position are required'}), 400
        
        job = JobApplication(
            company=data.get('company'),
            position=data.get('position'),
            location=data.get('location'),
            job_url=data.get('job_url'),
            description=data.get('description'),
            status=data.get('status', 'Applied'),
            salary_range=data.get('salary_range'),
            notes=data.get('notes'),
            contact_person=data.get('contact_person'),
            contact_email=data.get('contact_email')
        )
        
        # Handle application date
        if data.get('application_date'):
            job.application_date = datetime.fromisoformat(data['application_date'])
        
        # Handle tags
        tag_ids = data.get('tag_ids', [])
        if tag_ids:
            tags = Tag.query.filter(Tag.id.in_(tag_ids)).all()
            job.tags = tags
        
        db.session.add(job)
        db.session.commit()
        
        # Create history entry
        history = JobHistory(
            job_id=job.id,
            status=job.status,
            notes=f'Application created'
        )
        db.session.add(history)
        db.session.commit()
        
        return jsonify(job.to_dict()), 201
    
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@api_bp.route('/jobs/<int:job_id>', methods=['PUT'])
def update_job(job_id):
    """Update a job application"""
    try:
        job = JobApplication.query.get_or_404(job_id)
        data = request.get_json()
        
        old_status = job.status
        
        # Update fields
        for field in ['company', 'position', 'location', 'job_url', 'description', 
                     'status', 'salary_range', 'notes', 'contact_person', 'contact_email']:
            if field in data:
                setattr(job, field, data[field])
        
        # Handle application date
        if 'application_date' in data:
            job.application_date = datetime.fromisoformat(data['application_date'])
        
        # Handle tags
        if 'tag_ids' in data:
            tags = Tag.query.filter(Tag.id.in_(data['tag_ids'])).all()
            job.tags = tags
        
        job.last_updated = datetime.utcnow()
        
        # Create history entry if status changed
        if old_status != job.status:
            history = JobHistory(
                job_id=job.id,
                status=job.status,
                notes=f'Status changed from {old_status} to {job.status}'
            )
            db.session.add(history)
        
        db.session.commit()
        return jsonify(job.to_dict())
    
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@api_bp.route('/jobs/<int:job_id>', methods=['DELETE'])
def delete_job(job_id):
    """Delete a job application"""
    try:
        job = JobApplication.query.get_or_404(job_id)
        db.session.delete(job)
        db.session.commit()
        return '', 204
    
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# Tag endpoints
@api_bp.route('/tags', methods=['GET'])
def get_tags():
    """Get all tags"""
    try:
        tags = Tag.query.order_by(Tag.name).all()
        return jsonify([tag.to_dict() for tag in tags])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api_bp.route('/tags', methods=['POST'])
def create_tag():
    """Create a new tag"""
    try:
        data = request.get_json()
        
        if not data.get('name'):
            return jsonify({'error': 'Tag name is required'}), 400
        
        # Check if tag already exists
        existing_tag = Tag.query.filter_by(name=data['name']).first()
        if existing_tag:
            return jsonify({'error': 'Tag already exists'}), 400
        
        tag = Tag(
            name=data['name'],
            color=data.get('color', '#2563EB')
        )
        
        db.session.add(tag)
        db.session.commit()
        
        return jsonify(tag.to_dict()), 201
    
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@api_bp.route('/tags/<int:tag_id>', methods=['DELETE'])
def delete_tag(tag_id):
    """Delete a tag"""
    try:
        tag = Tag.query.get_or_404(tag_id)
        db.session.delete(tag)
        db.session.commit()
        return '', 204
    
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# History endpoints
@api_bp.route('/jobs/<int:job_id>/history', methods=['GET'])
def get_job_history(job_id):
    """Get history for a specific job"""
    try:
        history = JobHistory.query.filter_by(job_id=job_id).order_by(JobHistory.created_date.desc()).all()
        return jsonify([h.to_dict() for h in history])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Statistics endpoint
@api_bp.route('/statistics', methods=['GET'])
def get_statistics():
    """Get application statistics"""
    try:
        total_applications = JobApplication.query.count()
        
        status_counts = {}
        statuses = ['Applied', 'Interviewing', 'Offer', 'Rejected', 'Withdrawn']
        for status in statuses:
            count = JobApplication.query.filter_by(status=status).count()
            status_counts[status] = count
        
        # Applications this month
        from datetime import datetime, timedelta
        current_month = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        this_month_count = JobApplication.query.filter(
            JobApplication.application_date >= current_month
        ).count()
        
        return jsonify({
            'total_applications': total_applications,
            'status_counts': status_counts,
            'applications_this_month': this_month_count
        })
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# CSV Export endpoint
@api_bp.route('/export/csv', methods=['GET'])
def export_csv():
    """Export job applications to CSV"""
    try:
        jobs = JobApplication.query.order_by(JobApplication.application_date.desc()).all()
        
        output = io.StringIO()
        writer = csv.writer(output)
        
        # Write header
        writer.writerow([
            'Company', 'Position', 'Location', 'Status', 'Application Date',
            'Salary Range', 'Contact Person', 'Contact Email', 'Job URL', 'Notes', 'Tags'
        ])
        
        # Write data
        for job in jobs:
            tags_str = ', '.join([tag.name for tag in job.tags])
            writer.writerow([
                job.company,
                job.position,
                job.location or '',
                job.status,
                job.application_date.strftime('%Y-%m-%d') if job.application_date else '',
                job.salary_range or '',
                job.contact_person or '',
                job.contact_email or '',
                job.job_url or '',
                job.notes or '',
                tags_str
            ])
        
        output.seek(0)
        
        response = make_response(output.getvalue())
        response.headers['Content-Type'] = 'text/csv'
        response.headers['Content-Disposition'] = f'attachment; filename=job_applications_{datetime.now().strftime("%Y%m%d")}.csv'
        
        return response
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

from datetime import datetime
from database import db
from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey, Table
from sqlalchemy.orm import relationship

# Association table for many-to-many relationship between jobs and tags
job_tags = Table('job_tags', db.Model.metadata,
    Column('job_id', Integer, ForeignKey('job_application.id'), primary_key=True),
    Column('tag_id', Integer, ForeignKey('tag.id'), primary_key=True)
)

class JobApplication(db.Model):
    __tablename__ = 'job_application'
    
    id = Column(Integer, primary_key=True)
    company = Column(String(200), nullable=False)
    position = Column(String(200), nullable=False)
    location = Column(String(200))
    job_url = Column(Text)
    description = Column(Text)
    status = Column(String(50), nullable=False, default='Applied')
    salary_range = Column(String(100))
    application_date = Column(DateTime, default=datetime.utcnow)
    last_updated = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    notes = Column(Text)
    contact_person = Column(String(200))
    contact_email = Column(String(200))
    
    # Relationship with tags
    tags = relationship('Tag', secondary=job_tags, back_populates='jobs')
    
    # Relationship with history
    history = relationship('JobHistory', back_populates='job', cascade='all, delete-orphan')
    
    def to_dict(self):
        return {
            'id': self.id,
            'company': self.company,
            'position': self.position,
            'location': self.location,
            'job_url': self.job_url,
            'description': self.description,
            'status': self.status,
            'salary_range': self.salary_range,
            'application_date': self.application_date.isoformat() if self.application_date else None,
            'last_updated': self.last_updated.isoformat() if self.last_updated else None,
            'notes': self.notes,
            'contact_person': self.contact_person,
            'contact_email': self.contact_email,
            'tags': [tag.to_dict() for tag in self.tags]
        }

class Tag(db.Model):
    __tablename__ = 'tag'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False, unique=True)
    color = Column(String(7), default='#2563EB')  # Hex color code
    created_date = Column(DateTime, default=datetime.utcnow)
    
    # Relationship with jobs
    jobs = relationship('JobApplication', secondary=job_tags, back_populates='tags')
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'color': self.color,
            'created_date': self.created_date.isoformat() if self.created_date else None
        }

class JobHistory(db.Model):
    __tablename__ = 'job_history'
    
    id = Column(Integer, primary_key=True)
    job_id = Column(Integer, ForeignKey('job_application.id'), nullable=False)
    status = Column(String(50), nullable=False)
    notes = Column(Text)
    created_date = Column(DateTime, default=datetime.utcnow)
    
    # Relationship with job
    job = relationship('JobApplication', back_populates='history')
    
    def to_dict(self):
        return {
            'id': self.id,
            'job_id': self.job_id,
            'status': self.status,
            'notes': self.notes,
            'created_date': self.created_date.isoformat() if self.created_date else None
        }
